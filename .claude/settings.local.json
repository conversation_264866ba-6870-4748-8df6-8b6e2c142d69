{"permissions": {"allow": ["WebSearch", "Bash(npm run dev:*)", "mcp__chrome-mcp__chrome_get_web_content", "mcp__chrome-mcp__chrome_screenshot", "mcp__chrome-mcp__chrome_get_interactive_elements", "mcp__chrome-mcp__chrome_click_element", "mcp__chrome-mcp__chrome_console", "mcp__chrome-mcp__chrome_inject_script", "mcp__chrome-mcp__chrome_navigate", "Bash(npx eslint:*)", "Bash(npm run build:*)", "Bash(npm run lint)"], "deny": [], "ask": []}}