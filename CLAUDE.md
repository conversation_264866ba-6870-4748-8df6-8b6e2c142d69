# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React-based debug tool for testing and monitoring Tencent IM real-time messaging, designed to replace WebSocket functionality. The tool allows users to connect with different roles (Anchor/User), join groups, and monitor encrypted messages in real-time with filtering capabilities.

## Development Commands

### Start Development Server
```bash
cd app
npm run dev
```
This starts the Next.js development server with Turbopack on port 3000.

### Build
```bash
cd app
npm run build
```
Builds the production application with Turbopack.

### Lint
```bash
cd app
npm run lint
```
Runs ESLint to check code quality.

**Lint specific file:**
```bash
cd app
npx eslint src/path/to/file.ts
```
Runs ESLint on a specific file.

### Production Start
```bash
cd app
npm start
```
Starts the production server after building.

## Architecture

### Core Components Structure

**Main Component**: `TencentIMDebugTool.tsx` - The primary UI component that orchestrates the entire application flow:
- Handles connection form and settings
- Manages IM service lifecycle
- Coordinates message display and filtering
- Implements proper cleanup on disconnect

**Service Layer**: `TencentIMService.ts` - Encapsulates all Tencent IM SDK interactions:
- API endpoint selection based on role (host vs user)
- SDK initialization and authentication
- Group management (join/leave with proper cleanup)
- Message event handling and callback management

**Utility Functions**: `crypto.ts` - AES encryption/decryption utilities for message processing

**Supporting Components**:
- `MessageDisplay.tsx` - Renders message list with expandable JSON view
- `MessageFilters.tsx` - Provides filtering controls for event type, user ID, and user type

### API Integration Pattern

The application uses a role-based API endpoint selection:
- **User role (0)**: `/api/v1/ws_tencent_im/info`
- **Anchor role (1)**: `/host/v1/ws_tencent_im/info`

Required headers for all requests:
- `use-aes: false`
- `channel-id: 10013`
- `package-name: guming.yuanmu.com`
- `sub-channel-key: test1`
- `token: [user-token]`

### Message Processing Flow

1. **Receive**: Messages arrive through Tencent IM SDK custom message events
2. **Decrypt**: Use AES-CBC with key/IV from API response (`crypto.ts:decryptBase64`)
3. **Parse**: Convert decrypted JSON to structured message objects
4. **Filter**: Apply user-defined filters (event_type, user_id, user_type)
5. **Display**: Render in message list with timestamp and expandable content

### Group Management Logic

**User Role (role = 0)**:
- Automatically joins `im_group_all` from API response
- Optionally joins additional `groupId` if provided
- Must leave both groups on disconnect to prevent capacity issues

**Anchor Role (role = 1)**:
- No automatic group joining
- Handles anchor-specific functionality

### State Management

Uses React hooks for state management:
- Form data and connection status in main component
- Message array with real-time updates via SDK callbacks
- Filter state managed separately and applied to message display
- IM service instance stored in useRef for proper cleanup

### Critical Cleanup Requirements

**Always implement proper cleanup**:
- Leave all joined groups before disconnecting
- Call `tim.logout()` and `tim.destroy()` on SDK instance
- Clear message callbacks and reset state
- Handle cleanup in component unmount and window beforeunload

This is essential because `im_group_all` has member limitations and improper cleanup can cause capacity issues.

## Key Dependencies

- **Next.js 15**: React framework with App Router
- **tim-js-sdk**: Tencent IM JavaScript SDK for real-time messaging
- **crypto-js**: AES encryption/decryption for message processing
- **antd**: UI component library
- **axios**: HTTP client for API calls
- **Tailwind CSS**: Utility-first CSS framework

## Environment Structure

The project is structured as a Next.js app in the `/app` directory:
- Source code in `/app/src`
- Components in `/app/src/components`
- Services in `/app/src/services`
- Utilities in `/app/src/utils`
- App Router pages in `/app/src/app`

Always run npm commands from the `/app` directory, not the root.