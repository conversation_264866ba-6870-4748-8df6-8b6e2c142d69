{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@uiw/react-json-view": "^2.0.0-alpha.39", "antd": "^5.27.4", "axios": "^1.12.2", "crypto-js": "^4.2.0", "next": "15.5.4", "pako": "^2.1.0", "react": "19.1.0", "react-dom": "19.1.0", "tim-js-sdk": "^2.27.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/pako": "^2.0.4", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "tailwindcss": "^4", "typescript": "^5"}}