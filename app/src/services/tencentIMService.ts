import { apiCall } from '@/utils/apiClient';
import TIM from 'tim-js-sdk';

interface IMInfoRequest {
  domain: string;
  token: string;
  role: number;
  channelId: string;
  packageName: string;
  subChannelKey: string;
}

interface IMConfig {
  username: string;
  token: string;
  app_id: string;
  aes_key: string;
  aes_iv: string;
  im_group_all: string;
}

interface IMMessage {
  content: string;
  from: string;
  to: string;
  type: string;
}

type LogLevel = 'info' | 'success' | 'warning' | 'error';
type LogCallback = (message: string, level?: LogLevel) => void;

export class TencentIMService {
  private tim: any = null;
  private isInitialized = false;
  private messageCallback: ((message: IMMessage) => void) | null = null;
  private logCallback: LogCallback | null = null;

  constructor(logCallback?: LogCallback) {
    this.logCallback = logCallback || null;
  }

  private log(message: string, level: LogLevel = 'info'): void {
    if (this.logCallback) {
      this.logCallback(message, level);
    }
  }

  async getIMInfo(request: IMInfoRequest): Promise<IMConfig> {    
    this.log(`Fetching IM info from ${request.domain}`, 'info');
    
    const headers = {
      'use-aes': 'false',
      'channel-id': request.channelId,
      'package-name': request.packageName,
      'sub-channel-key': request.subChannelKey,
      'token': request.token
    };

    try {
      const response = await apiCall({
        url: request.domain,
        method: 'GET',
        headers
      });
      
      if (response.code !== 0) {
        this.log(`IM info API failed: ${response.msg || 'Unknown error'}`, 'error');
        throw new Error(response.msg || 'API request failed');
      }

      this.log(`IM info received - App ID: ${response.data.app_id}, Username: ${response.data.username}`, 'success');
      return response.data;
    } catch (error) {
      const errorMsg = `API Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      this.log(errorMsg, 'error');
      throw new Error(errorMsg);
    }
  }

  async initializeSDK(config: IMConfig): Promise<void> {
    if (this.isInitialized) {
      this.log('SDK already initialized, disconnecting first', 'info');
      await this.disconnect();
    }

    this.log(`Initializing Tencent IM SDK with App ID: ${config.app_id}`, 'info');

    try {
      this.tim = TIM.create({
        SDKAppID: parseInt(config.app_id)
      });

      this.tim.setLogLevel(0);
      this.log('SDK instance created successfully', 'success');

      this.tim.on(TIM.EVENT.SDK_READY, this.onSDKReady.bind(this));
      this.tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived.bind(this));
      this.tim.on(TIM.EVENT.SDK_NOT_READY, this.onSDKNotReady.bind(this));
      this.tim.on(TIM.EVENT.KICKED_OUT, this.onKickedOut.bind(this));
      this.tim.on(TIM.EVENT.ERROR, this.onError.bind(this));

      this.log('SDK event listeners registered', 'success');
      this.isInitialized = true;
    } catch (error) {
      const errorMsg = `Failed to initialize Tencent IM SDK: ${error}`;
      this.log(errorMsg, 'error');
      throw new Error(errorMsg);
    }
  }

  async authenticate(userID: string, userSig: string): Promise<void> {
    if (!this.tim) {
      const errorMsg = 'SDK not initialized';
      this.log(errorMsg, 'error');
      throw new Error(errorMsg);
    }

    this.log(`Authenticating user: ${userID}`, 'info');

    try {
      const response = await this.tim.login({
        userID,
        userSig
      });

      if (response.code !== 0) {
        const errorMsg = `Authentication failed: ${response.data}`;
        this.log(errorMsg, 'error');
        throw new Error(errorMsg);
      }

      this.log(`User ${userID} authenticated successfully`, 'success');
    } catch (error) {
      const errorMsg = `Authentication error: ${error}`;
      this.log(errorMsg, 'error');
      throw new Error(errorMsg);
    }
  }

  async joinGroup(groupID: string): Promise<void> {
    if (!this.tim) {
      const errorMsg = 'SDK not initialized';
      this.log(errorMsg, 'error');
      throw new Error(errorMsg);
    }

    this.log(`Attempting to join group: ${groupID}`, 'info');

    try {
      const response = await this.tim.joinGroup({
        groupID,
        type: TIM.TYPES.GRP_AVCHATROOM
      });

      if (response.code !== 0) {
        const errorMsg = `Failed to join group ${groupID}: ${response.data}`;
        this.log(errorMsg, 'error');
        throw new Error(errorMsg);
      }

      this.log(`Successfully joined group: ${groupID}`, 'success');
    } catch (error) {
      const errorMsg = `Join group error: ${error}`;
      this.log(errorMsg, 'error');
      throw new Error(errorMsg);
    }
  }

  async leaveGroup(groupID: string): Promise<void> {
    if (!this.tim) {
      const errorMsg = 'SDK not initialized';
      this.log(errorMsg, 'error');
      throw new Error(errorMsg);
    }

    this.log(`Attempting to leave group: ${groupID}`, 'info');

    try {
      const response = await this.tim.quitGroup(groupID);
      
      if (response.code !== 0) {
        const warnMsg = `Failed to leave group ${groupID}: ${response.data}`;
        this.log(warnMsg, 'warning');
        console.warn(warnMsg);
      } else {
        this.log(`Successfully left group: ${groupID}`, 'success');
      }
    } catch (error) {
      const warnMsg = `Leave group error: ${error}`;
      this.log(warnMsg, 'warning');
      console.warn(warnMsg);
    }
  }

  onMessage(callback: (message: IMMessage) => void): void {
    this.messageCallback = callback;
  }

  async disconnect(): Promise<void> {
    if (this.tim) {
      this.log('Initiating SDK disconnect', 'info');
      
      // Always attempt logout but handle errors gracefully
      try {
        await this.tim.logout();
        this.log('User logged out successfully', 'success');
      } catch (error) {
        // Handle the specific "user not logged in" error gracefully
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('not logged in')) {
          this.log('User was already logged out', 'info');
        } else {
          const warnMsg = `Logout error: ${errorMessage}`;
          this.log(warnMsg, 'warning');
          console.warn(warnMsg);
        }
      }
      
      try {
        this.tim.destroy();
        this.log('SDK instance destroyed', 'success');
      } catch (error) {
        const warnMsg = `SDK destroy error: ${error}`;
        this.log(warnMsg, 'warning');
        console.warn(warnMsg);
      }
      
      this.tim = null;
      this.isInitialized = false;
      this.messageCallback = null;
      this.log('SDK cleanup completed', 'success');
    } else {
      this.log('No active SDK connection to disconnect', 'info');
    }
  }

  private onSDKReady(): void {
    this.log('Tencent IM SDK is ready', 'success');
    console.log('Tencent IM SDK is ready');
  }

  private onSDKNotReady(): void {
    this.log('Tencent IM SDK is not ready', 'warning');
    console.log('Tencent IM SDK is not ready');
  }

  private onKickedOut(event: any): void {
    this.log(`User kicked out: ${JSON.stringify(event)}`, 'error');
    console.log('Kicked out:', event);
  }

  private onError(event: any): void {
    this.log(`Tencent IM Error: ${JSON.stringify(event)}`, 'error');
    console.error('Tencent IM Error:', event);
  }

  private onMessageReceived(event: any): void {
    const messageCount = event.data?.length || 0;
    this.log(`Message event received with ${messageCount} message(s)`, 'info');
    console.log('Received message:', event);
    
    if (!this.messageCallback) {
      this.log('No message callback registered, ignoring messages', 'warning');
      return;
    }

    const messages = event.data;
    messages.forEach((message: any) => {
      if (message.type === TIM.TYPES.MSG_CUSTOM) {
        const customData = message.payload?.data;
        if (customData) {
          this.log(`Processing custom message from ${message.from}`, 'info');
          this.messageCallback!({
            content: customData,
            from: message.from,
            to: message.to,
            type: message.type
          });
        } else {
          this.log(`Custom message from ${message.from} has no data`, 'warning');
        }
      } else {
        this.log(`Ignoring non-custom message type: ${message.type}`, 'info');
      }
    });
  }
}
