import { apiCall } from '@/utils/apiClient';

interface UserTokenRequest {
  device_id: string;
}

interface HostTokenRequest {
  mobile: string;
  password: string;
  code: string;
}

interface UserTokenResponse {
  code: number;
  msg: string;
  data: {
    token: string;
    [key: string]: any;
  };
}

interface HostTokenResponse {
  code: number;
  msg: string;
  data: {
    token: string;
    [key: string]: any;
  };
}

export class TokenService {
  static async getUserToken(): Promise<string> {
    const url = 'http://userapp.dcg888.com/front/login/visitor_login?test=1';
    
    const headers = {
      'channel-id': '10013',
      'package-name': 'android_hw',
      'sub-channel-key': 'aabb1234',
    };

    const requestData: UserTokenRequest = {
      device_id: 'afd34fed-90bb-443e-aea6-8a5d18361220'
    };

    try {
      const response: UserTokenResponse = await apiCall({
        url,
        method: 'POST',
        headers,
        body: requestData
      });
      
      if (response.code !== 0) {
        throw new Error(response.msg || 'Failed to get user token');
      }

      return response.data.token;
    } catch (error) {
      throw new Error(`User Token API Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static async getHostToken(): Promise<string> {
    const url = 'http://new-live-eks-dev-3ab4c477435d5dbe.elb.ap-east-1.amazonaws.com/host/v1/user/login';
    
    const headers = {
      'dev-use-aes': '0',
      'use-aes': 'false',
    };

    const requestData: HostTokenRequest = {
      mobile: '000001',
      password: 'vg002233',
      code: '123456'
    };

    try {
      const response: HostTokenResponse = await apiCall({
        url,
        method: 'POST',
        headers,
        body: requestData
      });
      
      if (response.code !== 0) {
        throw new Error(response.msg || 'Failed to get host token');
      }

      return response.data.token;
    } catch (error) {
      throw new Error(`Host Token API Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}