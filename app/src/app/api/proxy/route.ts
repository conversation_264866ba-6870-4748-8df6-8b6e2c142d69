import axios from 'axios';
import { NextRequest, NextResponse } from 'next/server';

interface ProxyRequest {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string>;
  curl_template?: string;
}

interface ProxyResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export async function POST(request: NextRequest): Promise<NextResponse<ProxyResponse>> {
  try {
    const proxyRequest: ProxyRequest = await request.json();

    if (!proxyRequest.url || !proxyRequest.method) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: url and method' },
        { status: 400 }
      );
    }

    let targetUrl = proxyRequest.url;
    
    if (proxyRequest.params && Object.keys(proxyRequest.params).length > 0) {
      const urlObj = new URL(targetUrl);
      Object.entries(proxyRequest.params).forEach(([key, value]) => {
        urlObj.searchParams.set(key, value);
      });
      targetUrl = urlObj.toString();
    }

    const axiosConfig: any = {
      method: proxyRequest.method,
      url: targetUrl,
      headers: {
        'Content-Type': 'application/json',
        ...proxyRequest.headers,
      },
    };

    if (proxyRequest.body && ['POST', 'PUT', 'PATCH'].includes(proxyRequest.method)) {
      axiosConfig.data = proxyRequest.body;
    }

    const response = await axios(axiosConfig);

    return NextResponse.json(
      { success: true, data: response.data },
      { 
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      }
    );

  } catch (error) {
    console.error('Proxy API Error:', error);

    let errorMessage = 'Internal server error';
    let statusCode = 500;

    if (axios.isAxiosError(error)) {
      errorMessage = error.response?.data?.msg || error.response?.data?.message || error.message;
      statusCode = error.response?.status || 500;
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { 
        status: statusCode,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}