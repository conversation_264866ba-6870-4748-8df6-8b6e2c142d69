'use client';

import { Card, List, Tag, Typography, <PERSON><PERSON>se, Badge, Avatar, Divider, Row, Col, Statistic } from 'antd';
import { ClockCircleOutlined, UserOutlined, MessageOutlined, TeamOutlined, TrophyOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface User {
  avatar: string;
  contribution_value: number;
  head_frame: string | null;
  hidden: number;
  is_noble: number;
  nickname: string;
  noble_name: string | null;
  noble_score: number;
  party_avatar: string;
  uid: number;
}

interface MessageData {
  list: User[];
  total: number;
  type: number;
}

interface Message {
  id: string;
  timestamp: number;
  event_type: number;
  client_id: string;
  target: string;
  data: MessageData;
  content: any;
  raw: string;
}

interface MessageDisplayProps {
  messages: Message[];
}

const MessageDisplay = ({ messages }: MessageDisplayProps) => {
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getEventTypeColor = (eventType: number) => {
    const colors: { [key: number]: string } = {
      1: 'blue',
      2: 'green', 
      3: 'orange',
      4: 'red',
      5: 'purple',
      6: 'cyan',
      7: 'magenta',
      8: 'gold'
    };
    return colors[eventType] || 'default';
  };

  const getEventTypeName = (eventType: number) => {
    const names: { [key: number]: string } = {
      1: 'User Join',
      2: 'User Leave',
      3: 'Message',
      4: 'Gift',
      5: 'Follow',
      6: 'User List',
      7: 'Room Update',
      8: 'System'
    };
    return names[eventType] || `Event ${eventType}`;
  };

  const renderUserList = (users: User[]) => {
    if (!users || users.length === 0) return null;
    
    return (
      <div className="space-y-2">
        <Text strong className="block">Users ({users.length}):</Text>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-60 overflow-y-auto">
          {users.map((user, index) => (
            <div key={user.uid || index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
              <Avatar 
                src={user.avatar} 
                size="small"
                icon={<UserOutlined />}
              />
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm truncate">
                  {user.nickname || 'Unknown'}
                </div>
                <div className="text-xs text-gray-500">
                  UID: {user.uid}
                  {user.is_noble ? (
                    <Tag color="gold" className="ml-1 text-xs">Noble</Tag>
                  ) : null}
                </div>
              </div>
              {user.contribution_value > 0 && (
                <div className="text-xs text-orange-600">
                  <TrophyOutlined /> {user.contribution_value}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderDataSummary = (message: Message) => {
    return (
      <div className="mb-3">
        <Row gutter={16}>
          <Col span={8}>
            <Statistic 
              title="Total" 
              value={message.data.total} 
              prefix={<TeamOutlined />}
            />
          </Col>
          <Col span={8}>
            <Statistic 
              title="Data Type" 
              value={message.data.type}
            />
          </Col>
          <Col span={8}>
            <Statistic 
              title="List Items" 
              value={message.data.list.length}
              prefix={<UserOutlined />}
            />
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <Card 
      title={
        <div className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <MessageOutlined />
            Messages
          </span>
          <Badge count={messages.length} showZero />
        </div>
      }
      className="shadow-md"
    >
      <div className="max-h-96 overflow-y-auto">
        {messages.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No messages received yet. Start the connection to see messages.
          </div>
        ) : (
          <List
            dataSource={messages}
            renderItem={(message) => (
              <List.Item key={message.id} className="border-b border-gray-100 last:border-b-0">
                <div className="w-full">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2 flex-wrap">
                      <Tag 
                        color={getEventTypeColor(message.event_type)}
                        className="font-mono"
                      >
                        {getEventTypeName(message.event_type)}
                      </Tag>
                      
                      <Tag color="purple">
                        Client: {message.client_id}
                      </Tag>
                      
                      <Tag color="orange">
                        Target: {message.target}
                      </Tag>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <ClockCircleOutlined />
                      {formatTimestamp(message.timestamp)}
                    </div>
                  </div>


                  {renderDataSummary(message)}

                  <Collapse size="small" ghost>
                    <Panel 
                      header={
                        <span className="text-sm">
                          View detailed content ({JSON.stringify(message.content).length} chars)
                        </span>
                      } 
                      key="content"
                    >
                      <div className="space-y-3">
                        {renderUserList(message.data.list)}
                        <Divider />
                        
                        <div>
                          <Text strong className="block mb-1">Complete Parsed Content:</Text>
                          <div className="bg-gray-50 p-3 rounded border overflow-auto">
                            <pre className="text-xs whitespace-pre-wrap font-mono">
                              {JSON.stringify(message.content, null, 2)}
                            </pre>
                          </div>
                        </div>
                        
                        <div>
                          <Text strong className="block mb-1">Raw Encrypted:</Text>
                          <div className="bg-gray-50 p-3 rounded border">
                            <Paragraph 
                              copyable 
                              className="text-xs font-mono break-all"
                              style={{ marginBottom: 0 }}
                            >
                              {message.raw}
                            </Paragraph>
                          </div>
                        </div>
                      </div>
                    </Panel>
                  </Collapse>
                </div>
              </List.Item>
            )}
          />
        )}
      </div>
    </Card>
  );
};

export default MessageDisplay;