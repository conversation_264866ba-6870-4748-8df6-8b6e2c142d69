'use client';

import { Modal, Button } from 'antd';
import JsonView from '@uiw/react-json-view';
import { memo, useMemo } from 'react';

interface MessageDetailsModalProps {
  visible: boolean;
  onClose: () => void;
  message: any;
  title: string;
}

const MessageDetailsModal = memo(({ visible, onClose, message, title }: MessageDetailsModalProps) => {
  const jsonViewStyle = useMemo(() => ({
    backgroundColor: '#f5f5f5',
    borderRadius: '6px',
    padding: '16px'
  }), []);

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="close" onClick={onClose}>
          Close
        </Button>
      ]}
    >
      <div className="max-h-96 overflow-y-auto">
        {visible && message && (
          <JsonView 
            value={message} 
            style={jsonViewStyle}
          />
        )}
      </div>
    </Modal>
  );
});

MessageDetailsModal.displayName = 'MessageDetailsModal';

export default MessageDetailsModal;