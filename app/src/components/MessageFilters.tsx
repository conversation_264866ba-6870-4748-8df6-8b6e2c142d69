'use client';

import { <PERSON>, Input, <PERSON><PERSON>, Badge, Di<PERSON>r, <PERSON>, Col } from 'antd';
import { FilterOutlined, ClearOutlined, DeleteOutlined, UserOutlined, MessageOutlined } from '@ant-design/icons';
import { useCallback, useMemo, memo, useState, useEffect } from 'react';
import { useDebounce } from '@/hooks/useDebounce';

interface Filters {
  eventType: string;
  userId: string;
  userType: string;
}

interface MessageFiltersProps {
  filters: Filters;
  onFiltersChange: (filters: Filters) => void;
  onClearMessages: () => void;
  messageCount: number;
  totalCount: number;
}

const MessageFilters = memo(({ 
  filters, 
  onFiltersChange, 
  onClearMessages,
  messageCount,
  totalCount
}: MessageFiltersProps) => {
  // Local state for immediate UI updates
  const [localFilters, setLocalFilters] = useState<Filters>(filters);
  
  // Debounced filters for actual filtering operations
  const debouncedFilters = useDebounce(localFilters, 300);
  
  // Update local filters when parent filters change (e.g., clear all)
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);
  
  // Trigger parent filter change when debounced filters change
  useEffect(() => {
    // Only call if there's an actual change to avoid infinite loops
    if (JSON.stringify(debouncedFilters) !== JSON.stringify(filters)) {
      onFiltersChange(debouncedFilters);
    }
  }, [debouncedFilters, filters, onFiltersChange]);
  
  const handleFilterChange = useCallback((field: keyof Filters, value: string) => {
    // Update local state immediately for responsive UI
    setLocalFilters(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const clearAllFilters = useCallback(() => {
    const clearedFilters = {
      eventType: '',
      userId: '',
      userType: ''
    };
    // Update both local and parent state immediately for clear action
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  }, [onFiltersChange]);

  const hasActiveFilters = useMemo(() => 
    Object.values(localFilters).some(filter => filter !== ''), [localFilters]);

  const filteredOutCount = useMemo(() => totalCount - messageCount, [totalCount, messageCount]);

  const activeFiltersList = useMemo(() => {
    const labels: { [key: string]: string } = {
      eventType: 'Event Type',
      userId: 'User ID',
      userType: 'User Type'
    };
    return Object.entries(localFilters)
      .filter(([, value]) => value)
      .map(([key, value]) => ({ key, label: labels[key], value }));
  }, [localFilters]);

  return (
    <Card 
      title={
        <div className="flex items-center gap-2 mt-8">
          <FilterOutlined />
          Advanced Message Filters
          {hasActiveFilters && <Badge count="Active" style={{ backgroundColor: '#52c41a' }} />}
        </div>
      }
      className="shadow-md"
    >
      {/* Message Filters */}
      <div className="mb-4">
        <h4 className="text-sm font-semibold mb-3 text-gray-700">Message Filters</h4>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <label className="block text-sm font-medium mb-1">
              <MessageOutlined className="mr-1" />
              Event Type
            </label>
            <Input
              value={localFilters.eventType}
              onChange={(e) => handleFilterChange('eventType', e.target.value)}
              onClear={() => handleFilterChange('eventType', '')}
              placeholder="e.g., 1, 6, 8"
              allowClear
            />
          </Col>
          
          <Col xs={24} sm={8}>
            <label className="block text-sm font-medium mb-1">
              <UserOutlined className="mr-1" />
              User ID
            </label>
            <Input
              value={localFilters.userId}
              onChange={(e) => handleFilterChange('userId', e.target.value)}
              onClear={() => handleFilterChange('userId', '')}
              placeholder="Filter by user ID"
              allowClear
            />
          </Col>
          
          <Col xs={24} sm={8}>
            <label className="block text-sm font-medium mb-1">
              User Type
            </label>
            <Input
              value={localFilters.userType}
              onChange={(e) => handleFilterChange('userType', e.target.value)}
              onClear={() => handleFilterChange('userType', '')}
              placeholder="e.g., 0, 1, 2"
              allowClear
            />
          </Col>
        </Row>
      </div>

      <Divider />

      {/* Controls */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button 
            icon={<ClearOutlined />}
            onClick={clearAllFilters}
            disabled={!hasActiveFilters}
          >
            Clear All Filters
          </Button>
          
          <Button 
            icon={<DeleteOutlined />}
            onClick={onClearMessages}
            danger
            type="text"
          >
            Clear All Messages
          </Button>
        </div>

        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span>
            Showing: <Badge count={messageCount} style={{ backgroundColor: '#1890ff' }} />
          </span>
          <span>
            Total: <Badge count={totalCount} style={{ backgroundColor: '#52c41a' }} />
          </span>
          {hasActiveFilters && (
            <span className="text-orange-600">
              ({filteredOutCount} filtered out)
            </span>
          )}
        </div>
      </div>

      {/* Filter Summary */}
      {hasActiveFilters && (
        <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
          <h5 className="text-sm font-semibold mb-2 text-blue-800">Active Filters:</h5>
          <div className="flex flex-wrap gap-2">
            {activeFiltersList.map(({ key, label, value }) => (
              <Badge 
                key={key}
                count={`${label}: ${value}`}
                style={{ backgroundColor: '#1890ff' }}
              />
            ))}
          </div>
        </div>
      )}
    </Card>
  );
});

MessageFilters.displayName = 'MessageFilters';

export default MessageFilters;