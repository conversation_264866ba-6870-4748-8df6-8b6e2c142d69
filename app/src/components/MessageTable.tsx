'use client';

import { <PERSON>, But<PERSON>, Tag, Card } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { useState, useMemo, useCallback, memo } from 'react';
import MessageDetailsModal from './MessageDetailsModal';

interface Message {
  id: string;
  timestamp: number;
  event_type: number;
  client_id: string;
  target: string;
  data: any;
  content: any;
  raw: string;
}

interface MessageTableProps {
  messages: Message[];
}

const MessageTable = memo(({ messages }: MessageTableProps) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);

  const formatTimestamp = useCallback((timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  }, []);

  const getEventTypeColor = useCallback((eventType: number) => {
    const colors: { [key: number]: string } = {
      1: 'blue',
      2: 'green', 
      3: 'orange',
      4: 'red',
      5: 'purple',
      6: 'cyan',
      7: 'magenta',
      8: 'gold'
    };
    return colors[eventType] || 'default';
  }, []);

  const handleShowDetails = useCallback((message: Message) => {
    setSelectedMessage(message);
    setModalVisible(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setModalVisible(false);
  }, []);

  const columns = useMemo(() => [
    {
      title: 'Time',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      render: (timestamp: number) => formatTimestamp(timestamp),
    },
    {
      title: 'Event Type',
      dataIndex: 'event_type',
      key: 'event_type',
      width: 120,
      render: (eventType: number) => (
        <Tag color={getEventTypeColor(eventType)}>
          {eventType}
        </Tag>
      ),
    },
    {
      title: 'User ID',
      dataIndex: 'content',
      key: 'target',
      width: 150,
      render: (content: any) => content?.user_id || '-',
    },
    {
      title: 'User Type',
      dataIndex: 'content',
      key: 'user_type',
      width: 120,
      render: (content: any) => content?.user_type || '-',
    },
    {
      title: 'Details',
      key: 'details',
      width: 100,
      render: (_: any, record: Message) => (
        <Button
          type="primary"
          icon={<EyeOutlined />}
          size="small"
          onClick={() => handleShowDetails(record)}
        >
          Details
        </Button>
      ),
    },
  ], [formatTimestamp, getEventTypeColor, handleShowDetails]);

  return (
    <Card title="Messages">
      <Table
        columns={columns}
        dataSource={messages}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} items`,
        }}
        scroll={{ y: 400 }}
        size="small"
      />
      
      <MessageDetailsModal
        visible={modalVisible}
        onClose={handleCloseModal}
        message={selectedMessage?.content}
        title={`Message Details - Event Type: ${selectedMessage?.event_type}`}
      />
    </Card>
  );
});

MessageTable.displayName = 'MessageTable';

export default MessageTable;