"use client";

import { TencentIMService } from "@/services/tencentIMService";
import { TokenService } from "@/services/tokenService";
import { decryptBase64 } from "@/utils/crypto";
import { InfoCircleOutlined } from "@ant-design/icons";
import type { RadioChangeEvent } from "antd";
import {
  <PERSON><PERSON>,
  Badge,
  Button,
  Card,
  Divider,
  Input,
  Radio,
  Space,
  Switch,
  Typography,
} from "antd";
import { useEffect, useRef, useState, useMemo } from "react";
import MessageTable from "./MessageTable";
import MessageFilters from "./MessageFilters";

interface IMConfig {
  username: string;
  token: string;
  app_id: string;
  aes_key: string;
  aes_iv: string;
  im_group_all: string;
}

interface FormData {
  domain: string;
  token: string;
  role: number;
  groupId: string;
}

interface User {
  avatar: string;
  contribution_value: number;
  head_frame: string | null;
  hidden: number;
  is_noble: number;
  nickname: string;
  noble_name: string | null;
  noble_score: number;
  party_avatar: string;
  uid: number;
}

interface MessageData {
  list: User[];
  total: number;
  type: number;
}

interface Message {
  id: string;
  timestamp: number;
  event_type: number;
  client_id: string;
  target: string;
  data: MessageData;
  content: any;
  raw: string;
}

interface Filters {
  eventType: string;
  clientId: string;
  target: string;
  dataType: string;
}

interface TokenLoading {
  user: boolean;
  host: boolean;
}

interface PresetLoading {
  hostApp: boolean;
  userApp: boolean;
}

interface LogEntry {
  id: string;
  timestamp: number;
  message: string;
  type: "info" | "success" | "warning" | "error";
}

const HOST_DOMAIN =
  "http://new-live-eks-dev-3ab4c477435d5dbe.elb.ap-east-1.amazonaws.com/host/v1/ws_tencent_im/info";
const USER_DOMAIN =
  "http://new-live-eks-dev-3ab4c477435d5dbe.elb.ap-east-1.amazonaws.com/api/v1/ws_tencent_im/info";

const MAX_LOGS = 500;
const MAX_MESSAGES = 500;

const TencentIMDebugTool = () => {
  const [formData, setFormData] = useState<FormData>({
    domain: "",
    token: "",
    role: 1,
    groupId: "",
  });

  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<string>("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [filters, setFilters] = useState<Filters>({
    eventType: "",
    clientId: "",
    target: "",
    dataType: "",
  });
  const [imConfig, setImConfig] = useState<IMConfig | null>(null);
  const [error, setError] = useState<string>("");
  const [tokenLoading, setTokenLoading] = useState<TokenLoading>({
    user: false,
    host: false,
  });
  const [presetLoading, setPresetLoading] = useState<PresetLoading>({
    hostApp: false,
    userApp: false,
  });
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [showLogsPanel, setShowLogsPanel] = useState(true);
  const [tokenError, setTokenError] = useState<string>("");

  const imServiceRef = useRef<TencentIMService | null>(null);

  useEffect(() => {
    // Crypto functions updated - no test functions needed
  }, []);

  const handleInputChange = (field: keyof FormData, value: string | number) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const addLog = (message: string, type: LogEntry["type"] = "info") => {
    const newLog: LogEntry = {
      id: `${Date.now()}-${Math.random()}`,
      timestamp: Date.now(),
      message,
      type,
    };
    setLogs((prev) => {
      const newLogs = [newLog, ...prev];
      // Keep only the most recent MAX_LOGS entries
      return newLogs.length > MAX_LOGS ? newLogs.slice(0, MAX_LOGS) : newLogs;
    });
  };

  const setConnectionStatusWithLog = (
    status: string,
    logType: LogEntry["type"] = "info"
  ) => {
    setConnectionStatus(status);
    addLog(status, logType);
  };

  const handleRoleChange = (e: RadioChangeEvent) => {
    setFormData((prev) => ({
      ...prev,
      role: e.target.value,
    }));
  };

  const handleConnect = async () => {
    if (!formData.domain || !formData.token) {
      setError("Domain and token are required");
      return;
    }

    setIsConnecting(true);
    setError("");
    setConnectionStatusWithLog("Connecting to Tencent IM...");

    try {
      const imService = new TencentIMService(addLog);
      imServiceRef.current = imService;

      const config = await imService.getIMInfo({
        domain: formData.domain,
        token: formData.token,
        role: formData.role,
        channelId: "10013",
        packageName: "guming.yuanmu.com",
        subChannelKey: "test1",
      });

      setImConfig(config);
      setConnectionStatusWithLog("Initializing SDK...");

      await imService.initializeSDK(config);
      setConnectionStatusWithLog("Authenticating...");

      await imService.authenticate(config.username, config.token);
      setConnectionStatusWithLog(`Joining groups ${config.im_group_all}...`);

      await imService.joinGroup(config.im_group_all);
      if (formData.groupId) {
        setConnectionStatusWithLog(`Joining group ${formData.groupId}...`);
        await imService.joinGroup(formData.groupId);
      }

      imService.onMessage((message) => {
        console.log("Received message:", message);
        addLog(`Received message from ${message.from || "unknown"}`, "info");
        try {
          const decryptedData = decryptBase64(
            message.content,
            config.aes_key,
            config.aes_iv
          );
          const parsedMessages = JSON.parse(decryptedData);
          
          addLog(
            `Message decrypted - Found: ${Array.isArray(parsedMessages) ? parsedMessages.length : 1} message(s)`,
            "success"
          );

          // Handle both single message and array of messages
          const messagesArray = Array.isArray(parsedMessages) ? parsedMessages : [parsedMessages];
          
          const newMessages = messagesArray.map((parsedMessage: any) => ({
            id: `${Date.now()}-${Math.random()}`,
            timestamp: Date.now(),
            event_type: parsedMessage.event_type,
            client_id: parsedMessage.client_id,
            target: parsedMessage.target,
            data: parsedMessage.data,
            content: parsedMessage,
            raw: message.content,
          }));

          setMessages((prev) => {
            const updatedMessages = [...newMessages, ...prev];
            // Keep only the most recent MAX_MESSAGES entries
            return updatedMessages.length > MAX_MESSAGES 
              ? updatedMessages.slice(0, MAX_MESSAGES) 
              : updatedMessages;
          });
        } catch (err) {
          console.error("Failed to decrypt message:", err);
          addLog(
            `Failed to decrypt message: ${
              err instanceof Error ? err.message : "Unknown error"
            }`,
            "error"
          );
        }
      });

      setIsConnected(true);
      setConnectionStatusWithLog(
        "Connected and listening for messages",
        "success"
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : "Connection failed");
      addLog(err instanceof Error ? err.message : "Connection failed", "error");
      setConnectionStatus("");
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    if (imServiceRef.current && imConfig) {
      try {
        setConnectionStatusWithLog("Disconnecting...");

        if (formData.role === 0) {
          await imServiceRef.current.leaveGroup(imConfig.im_group_all);
          if (formData.groupId) {
            await imServiceRef.current.leaveGroup(formData.groupId);
          }
        }

        await imServiceRef.current.disconnect();
        imServiceRef.current = null;
      } catch (err) {
        console.error("Disconnect error:", err);
      }
    }

    setIsConnected(false);
    addLog("Disconnected", "info");
    setConnectionStatus("");
    setMessages([]);
    setImConfig(null);
  };

  const handleClearMessages = () => {
    setMessages([]);
  };

  const handleClearLogs = () => {
    setLogs([]);
  };

  const handleFetchUserToken = async () => {
    setTokenLoading((prev) => ({ ...prev, user: true }));
    setTokenError("");
    addLog("Fetching user token...", "info");

    try {
      const token = await TokenService.getUserToken();
      handleInputChange("token", token);
      addLog("User token fetched successfully", "success");
      setTokenError("");
    } catch (err) {
      const errorMsg =
        err instanceof Error ? err.message : "Failed to fetch user token";
      setTokenError(errorMsg);
      addLog(`User token fetch failed: ${errorMsg}`, "error");
    } finally {
      setTokenLoading((prev) => ({ ...prev, user: false }));
    }
  };

  const handleFetchHostToken = async () => {
    setTokenLoading((prev) => ({ ...prev, host: true }));
    setTokenError("");
    addLog("Fetching host token...", "info");

    try {
      const token = await TokenService.getHostToken();
      handleInputChange("token", token);
      addLog("Host token fetched successfully", "success");
      setTokenError("");
    } catch (err) {
      const errorMsg =
        err instanceof Error ? err.message : "Failed to fetch host token";
      setTokenError(errorMsg);
      addLog(`Host token fetch failed: ${errorMsg}`, "error");
    } finally {
      setTokenLoading((prev) => ({ ...prev, host: false }));
    }
  };

  const handleHostAppPreset = async () => {
    setPresetLoading((prev) => ({ ...prev, hostApp: true }));
    setTokenError("");
    setError("");
    addLog("Setting up Host App preset...", "info");

    try {
      // Set host domain
      handleInputChange("domain", HOST_DOMAIN);
      addLog("Host domain configured", "success");

      // Set role to Anchor (1)
      handleInputChange("role", 1);
      addLog("Role set to Anchor (1)", "success");

      // Fetch host token
      const token = await TokenService.getHostToken();
      handleInputChange("token", token);
      addLog("Host App preset setup completed", "success");

      setTokenError("");
    } catch (err) {
      const errorMsg =
        err instanceof Error ? err.message : "Failed to setup Host App preset";
      setTokenError(errorMsg);
      addLog(`Host App preset setup failed: ${errorMsg}`, "error");
    } finally {
      setPresetLoading((prev) => ({ ...prev, hostApp: false }));
    }
  };

  const handleUserAppPreset = async () => {
    setPresetLoading((prev) => ({ ...prev, userApp: true }));
    setTokenError("");
    setError("");
    addLog("Setting up User App preset...", "info");

    try {
      // Set user domain
      handleInputChange("domain", USER_DOMAIN);
      addLog("User domain configured", "success");

      // Set role to User (0)
      handleInputChange("role", 0);
      addLog("Role set to User (0)", "success");

      // Fetch user token
      const token = await TokenService.getUserToken();
      handleInputChange("token", token);
      addLog("User App preset setup completed", "success");

      setTokenError("");
    } catch (err) {
      const errorMsg =
        err instanceof Error ? err.message : "Failed to setup User App preset";
      setTokenError(errorMsg);
      addLog(`User App preset setup failed: ${errorMsg}`, "error");
    } finally {
      setPresetLoading((prev) => ({ ...prev, userApp: false }));
    }
  };

  useEffect(() => {
    return () => {
      handleDisconnect();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const filteredMessages = useMemo(() => {
    return messages.filter((message) => {
      if (filters.eventType && message.event_type?.toString() !== filters.eventType)
        return false;
      if (filters.clientId && message.client_id !== filters.clientId)
        return false;
      if (filters.target && message.target !== filters.target)
        return false;
      if (filters.dataType && message.data?.type?.toString() !== filters.dataType)
        return false;
      return true;
    });
  }, [messages, filters.eventType, filters.clientId, filters.target, filters.dataType]);

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getLogTypeColor = (type: LogEntry["type"]) => {
    switch (type) {
      case "success":
        return "text-green-600";
      case "error":
        return "text-red-600";
      case "warning":
        return "text-yellow-600";
      default:
        return "text-blue-600";
    }
  };

  const LogsPanel = () => (
    <Card
      title={
        <div className="flex justify-between items-center">
          <span>Logs ({logs.length})</span>
          <div className="flex gap-2">
            <Button size="small" onClick={handleClearLogs}>
              Clear
            </Button>
          </div>
        </div>
      }
      className="shadow-md h-full"
      styles={{ body: { padding: "0", maxHeight: "80vh", overflowY: "auto" } }}
    >
      {logs.length === 0 ? (
        <div className="p-4">
          <Typography.Text type="secondary">No logs yet</Typography.Text>
        </div>
      ) : (
        <div className="p-4">
          <div className="space-y-2 max-h-[100vh] overflow-y-auto">
            {logs.map((log) => (
              <div
                key={log.id}
                className="p-2 border-l-4 bg-gray-50 text-xs"
                style={{
                  borderLeftColor:
                    log.type === "success"
                      ? "#10b981"
                      : log.type === "error"
                      ? "#ef4444"
                      : log.type === "warning"
                      ? "#f59e0b"
                      : "#3b82f6",
                }}
              >
                <div className="flex justify-between items-start mb-1">
                  <span className={`font-medium ${getLogTypeColor(log.type)}`}>
                    {log.type.toUpperCase()}
                  </span>
                  <span className="text-gray-500">
                    {formatTimestamp(log.timestamp)}
                  </span>
                </div>
                <div
                  className="text-gray-700"
                  style={{ whiteSpace: "pre-wrap" }}
                >
                  {log.message}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </Card>
  );

  return (
    <div className="max-w-full mx-auto p-4 flex gap-6">
      {/* Main Content */}
      <div className={`${showLogsPanel ? "w-2/3" : "w-full"} space-y-6`}>
        <Card title="Connection Settings" className="shadow-md m">
          {!isConnected ? (
            <>
              {/* Quick Setup Section */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-semibold mb-3">Quick Setup</h3>
                <div className="flex flex-wrap gap-4">
                  <Button
                    type="primary"
                    size="large"
                    loading={presetLoading.hostApp}
                    onClick={handleHostAppPreset}
                    className="bg-orange-500 hover:bg-orange-600 border-orange-500 hover:border-orange-600"
                  >
                    {presetLoading.hostApp
                      ? "Setting up..."
                      : "Initial Test for Host App"}
                  </Button>
                  <Button
                    type="primary"
                    size="large"
                    loading={presetLoading.userApp}
                    onClick={handleUserAppPreset}
                    className="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
                  >
                    {presetLoading.userApp
                      ? "Setting up..."
                      : "Initial Test for User App"}
                  </Button>
                </div>
                <div className="mt-2 text-sm text-gray-600">
                  One-click setup: automatically configures domain, token, and role
                  for testing
                </div>
              </div>

              <Divider />

              <h3 className="text-lg font-semibold mb-4">Manual Configuration</h3>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Domain
                    <InfoCircleOutlined className="ml-1 text-gray-400" />
                  </label>

                  <div>
                    <div className="mb-2 text-sm text-gray-500">Quick Select:</div>
                    <Space className="mb-2">
                      <Button
                        size="small"
                        onClick={() => handleInputChange("domain", HOST_DOMAIN)}
                      >
                        Host app
                      </Button>
                      <Button
                        size="small"
                        onClick={() => handleInputChange("domain", USER_DOMAIN)}
                      >
                        User app
                      </Button>
                    </Space>
                  </div>

                  <Input
                    value={formData.domain}
                    onChange={(e) => handleInputChange("domain", e.target.value)}
                    placeholder="Enter domain URL"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Token</label>

                  <div>
                    <div className="mb-2 text-sm text-gray-500">Quick Select:</div>
                    <Space className="mb-2">
                      <Button
                        size="small"
                        loading={tokenLoading.host}
                        onClick={handleFetchHostToken}
                      >
                        Get Host Token
                      </Button>
                      <Button
                        size="small"
                        loading={tokenLoading.user}
                        onClick={handleFetchUserToken}
                      >
                        Get User Token
                      </Button>
                    </Space>
                  </div>

                  <Input
                    value={formData.token}
                    onChange={(e) => handleInputChange("token", e.target.value)}
                    placeholder="Enter authentication token"
                  />

                  {tokenError && (
                    <Alert
                      message={tokenError}
                      type="error"
                      showIcon
                      className="mt-2"
                      closable
                      onClose={() => setTokenError("")}
                    />
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Role</label>
                  <Radio.Group
                    value={formData.role}
                    onChange={handleRoleChange}
                  >
                    <Radio value={1}>Anchor (1)</Radio>
                    <Radio value={0}>User (0)</Radio>
                  </Radio.Group>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Group ID (Optional)
                  </label>
                  <Input
                    value={formData.groupId}
                    onChange={(e) => handleInputChange("groupId", e.target.value)}
                    placeholder="Enter group ID"
                  />
                </div>
              </div>

              <Divider />

              <div className="flex flex-wrap gap-4 items-center">
                <Button
                  type="primary"
                  onClick={handleConnect}
                  loading={isConnecting}
                  size="large"
                >
                  {isConnecting ? "Connecting..." : "Start Receiving Messages"}
                </Button>
              </div>

              {error && (
                <Alert
                  message="Connection Error"
                  description={error}
                  type="error"
                  showIcon
                  className="mt-4"
                />
              )}
            </>
          ) : (
            /* Connected State - Minimal UI */
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Badge status="success" text="Connected to Tencent IM" />
                <span className="text-sm text-gray-600">Role: {formData.role === 1 ? 'Anchor' : 'User'}</span>
                {connectionStatus && (
                  <span className="text-sm text-gray-600">{connectionStatus}</span>
                )}
              </div>
              <Button danger onClick={handleDisconnect} size="large">
                Disconnect
              </Button>
            </div>
          )}
        </Card>

        {isConnected && (
          <Space direction="vertical" size="middle">
            <MessageFilters
              filters={filters}
              onFiltersChange={setFilters}
              onClearMessages={handleClearMessages}
              messageCount={filteredMessages.length}
              totalCount={messages.length}
            />

            <MessageTable messages={filteredMessages} />
          </Space>
        )}
      </div>

      {/* Logs Panel */}
      <div className="w-1/3">
        <div className="space-x-2 mb-4">
          <span>Show Logs</span>
          <Switch
            checked={showLogsPanel}
            onChange={setShowLogsPanel}
            size="small"
          />
        </div>
        {showLogsPanel && <LogsPanel />}
      </div>
    </div>
  );
};

export default TencentIMDebugTool;
