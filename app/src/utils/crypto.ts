import pako from 'pako';
import CryptoJS from 'crypto-js'

export function aesEncrypt(data: Uint8Array, aesKey: string = "", aesIv: string = ""): ArrayBuffer {
  const _key = CryptoJS.enc.Base64.parse(aesKey);
  const _iv = CryptoJS.enc.Base64.parse(aesIv);

  const encrypted = CryptoJS.AES.encrypt(CryptoJS.lib.WordArray.create(data), _key, {
      iv: _iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
  });
  const ciphertext = encrypted.ciphertext;

  const encryptedArray = new Uint8Array(ciphertext.sigBytes);
  for (let i = 0; i < ciphertext.sigBytes; i++) {
    encryptedArray[i] = ciphertext.words[i >>> 2] >>> (24 - (i % 4) * 8) & 0xff;
  }
  console.log(encryptedArray)
  return encryptedArray.buffer
}

export function aesDecrypt(binaryData: Uint8Array, aesKey: string = "", aesIv: string = ""): Uint8Array {
  const _key = CryptoJS.enc.Base64.parse(aesKey);
  const _iv = CryptoJS.enc.Base64.parse(aesIv);

  const cipherParams = CryptoJS.lib.CipherParams.create({
    ciphertext: CryptoJS.lib.WordArray.create(binaryData)
  });
  
  const decrypted = CryptoJS.AES.decrypt(cipherParams, _key, {
    iv: _iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });

  const decryptedArray = new Uint8Array(decrypted.sigBytes);
  for (let i = 0; i < decrypted.sigBytes; i++) {
    decryptedArray[i] = (decrypted.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
  }

  return decryptedArray
}

export function ungzipDecompress(uint8Array: Uint8Array): string {
  return pako.ungzip(uint8Array, { to: 'string' });
}

export function gzipDecompress(text: string): Uint8Array {
  return pako.gzip(text);
}

// Helper function to decrypt base64 data with AES and decompress
export function decryptBase64(base64String: string, aesKey: string, aesIv: string): string {
  // Convert base64 string to binary data
  const binaryData = Uint8Array.from(atob(base64String), c => c.charCodeAt(0));
  
  // Decrypt the binary data with AES
  const decryptedData = aesDecrypt(binaryData, aesKey, aesIv);
  
  // Decompress the decrypted data
  const decompressedData = ungzipDecompress(decryptedData);
  
  return decompressedData;
}
