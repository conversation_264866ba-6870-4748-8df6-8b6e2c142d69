import axios, { AxiosResponse } from 'axios';
import { generateCurl } from './curlGenerator';

interface ApiCallConfig {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string>;
}

interface ProxyResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export async function apiCall(config: ApiCallConfig): Promise<any> {
  const useProxy = process.env.NEXT_PUBLIC_USE_PROXY === 'true';

  if (useProxy) {
    return callViaProxy(config);
  } else {
    return callDirect(config);
  }
}

async function callViaProxy(config: ApiCallConfig): Promise<any> {
  try {
    const curlCommand = generateCurl(config);
    
    const response: AxiosResponse<ProxyResponse> = await axios.post('/api/proxy', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      body: config.body,
      params: config.params,
      curl_template: curlCommand,
    });

    if (!response.data.success) {
      throw new Error(response.data.error || 'Proxy request failed');
    }

    return response.data.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.data?.error) {
      throw new Error(error.response.data.error);
    }
    throw error;
  }
}

async function callDirect(config: ApiCallConfig): Promise<any> {
  try {
    let targetUrl = config.url;
    
    if (config.params && Object.keys(config.params).length > 0) {
      const urlObj = new URL(targetUrl);
      Object.entries(config.params).forEach(([key, value]) => {
        urlObj.searchParams.set(key, value);
      });
      targetUrl = urlObj.toString();
    }

    const axiosConfig: any = {
      method: config.method,
      url: targetUrl,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    };

    if (config.body && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
      axiosConfig.data = config.body;
    }

    const response = await axios(axiosConfig);
    return response.data;
  } catch (error) {
    throw error;
  }
}