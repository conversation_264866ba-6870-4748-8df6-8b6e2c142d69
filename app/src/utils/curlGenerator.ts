interface CurlConfig {
  url: string;
  method: string;
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string>;
}

export function generateCurl(config: CurlConfig): string {
  let targetUrl = config.url;
  
  // Add query parameters to URL if provided
  if (config.params && Object.keys(config.params).length > 0) {
    const urlObj = new URL(targetUrl);
    Object.entries(config.params).forEach(([key, value]) => {
      urlObj.searchParams.set(key, value);
    });
    targetUrl = urlObj.toString();
  }

  // Start building curl command
  let curl = `curl --location '${targetUrl}'`;

  // Add HTTP method if not GET
  if (config.method && config.method !== 'GET') {
    curl += ` \\\n--request ${config.method}`;
  }

  // Add headers
  if (config.headers && Object.keys(config.headers).length > 0) {
    Object.entries(config.headers).forEach(([key, value]) => {
      curl += ` \\\n--header '${key}: ${value}'`;
    });
  }

  // Add Content-Type header if not already present and we have a body
  if (config.body && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
    const hasContentType = config.headers && 
      Object.keys(config.headers).some(key => key.toLowerCase() === 'content-type');
    
    if (!hasContentType) {
      curl += ` \\\n--header 'Content-Type: application/json'`;
    }
  }

  // Add body data for methods that support it
  if (config.body && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
    const bodyString = typeof config.body === 'string' 
      ? config.body 
      : JSON.stringify(config.body, null, 2);
    
    // Escape single quotes in the body
    const escapedBody = bodyString.replace(/'/g, "'\"'\"'");
    curl += ` \\\n--data '${escapedBody}'`;
  }

  return curl;
}