# Tencent IM Debug Tool

A React-based debug tool for testing and monitoring Tencent IM real-time messaging, designed to replace WebSocket functionality.

## Features

- 🔐 **Authentication**: Connect using domain and token
- 👥 **Role-based Access**: Support for both Anchor (1) and User (0) roles  
- 🔍 **Message Filtering**: Filter messages by event type, user ID, and user type
- 🔄 **Real-time Display**: Live message monitoring with decryption
- 🏠 **Group Management**: Automatic group joining/leaving with proper cleanup
- 📊 **Message Analytics**: View parsed content and raw encrypted data

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser

## Usage

### 1. Connection Setup

Fill in the required fields:

- **Domain**: API endpoint (default: `http://new-live-eks-dev-3ab4c477435d5dbe.elb.ap-east-1.amazonaws.com`)
- **Token**: Your authentication token
- **Role**: 
  - `User (0)`: Will automatically join `im_group_all` and optionally a specific group
  - `Anchor (1)`: Anchor-specific functionality
- **Group ID**: (Optional) Additional group to join for User role

### 2. Start Receiving Messages

Click "Start Receiving Messages" to:
- Call the appropriate API endpoint based on role
- Initialize Tencent IM SDK with returned credentials
- Authenticate and join groups
- Begin real-time message monitoring

### 3. Message Monitoring

- **Real-time Display**: Messages appear as they arrive
- **Automatic Decryption**: Uses AES key/IV from API response
- **Message Details**: View both parsed content and raw encrypted data
- **Filtering**: Filter by event type, user ID, or user type
- **Analytics**: See message counts and filtering statistics

### 4. Cleanup

Click "Disconnect" to properly:
- Leave all joined groups
- Disconnect from Tencent IM
- Clear message history

⚠️ **Important**: Always disconnect properly to avoid group capacity issues.

## API Endpoints

### User Role (role = 0)
```
POST /api/v1/ws_tencent_im/info
```

### Anchor Role (role = 1)  
```
POST /host/v1/ws_tencent_im/info
```

### Required Headers
```
use-aes: false
channel-id: 10013
package-name: guming.yuanmu.com
sub-channel-key: test1
token: [your-token]
```

## Technical Stack

- **Frontend**: Next.js 15 + React 18 + TypeScript
- **UI Library**: Ant Design
- **Styling**: Tailwind CSS
- **IM SDK**: Tencent IM JavaScript SDK
- **Encryption**: CryptoJS for AES decryption
- **HTTP Client**: Axios

## Troubleshooting

### Connection Issues
- Verify domain URL is accessible
- Check token validity and format
- Ensure network connectivity

### Message Not Appearing
- Confirm proper group joining (check console logs)
- Verify AES key/IV are correctly applied
- Check message filtering settings

### Group Capacity Issues
- Always disconnect properly when closing the app
- `im_group_all` has member limitations
- Use proper cleanup procedures
