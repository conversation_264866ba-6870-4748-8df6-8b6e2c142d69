# Tencent IM Debug Tool - Project Requirements

## Overview
This project is a debugging tool designed to replace WebSocket functionality with Tencent IM for real-time message transfer. The tool allows testing and monitoring of Tencent IM message flows in real-time.

## API Endpoints

### Host Application
```
POST http://new-live-eks-dev-3ab4c477435d5dbe.elb.ap-east-1.amazonaws.com/host/v1/ws_tencent_im/info
```

### User Application
```
POST http://new-live-eks-dev-3ab4c477435d5dbe.elb.ap-east-1.amazonaws.com/api/v1/ws_tencent_im/info
```

### Required Headers
- `use-aes: false`
- `channel-id: 10013`
- `package-name: guming.yuanmu.com`
- `sub-channel-key: test1`
- `token: [user-specific-token]`

### API Response Format
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": {
        "username": "or6tfjyasmjvlvfeia2e9soqbiwftmje",
        "token": "eJwszlFLhjAUxvHvcq7j7...",
        "app_id": "20027902",
        "aes_key": "Mw/BR8yKFoP1ILtuQAGiTA==",
        "aes_iv": "bo1NaNISE3gN4SU07nsKzw==",
        "im_group_all": "@TGS#2EWB2PH5CB"
    }
}
```

## UI Requirements

### Input Fields
1. **Domain** - Server domain URL
2. **Token** - Authentication token
3. **Role** - User role selection
   - Anchor: 1
   - User: 0
4. **Group ID** (Optional) - Specific group identifier

### Controls
- **"Receive Messages" Button** - Initiates the message receiving process

## Core Functionality

### 1. Tencent IM SDK Integration
- Initialize Tencent IM SDK using API response data
- Handle authentication with provided credentials

### 2. Message Processing
- **Decrypt Messages**: Use AES key and IV from API response
- **Decompress Messages**: Handle batch messages similar to WebSocket implementation
- **Display Messages**: Show each message item on a separate line

### 3. Group Management
#### User Role (role = 0)
- Automatically join `im_group_all` group to receive all messages (event_type = 8)
- Join specific `groupID` if provided
- Gracefully leave both groups when session ends

#### Anchor Role (role = 1)
- Handle anchor-specific functionality
- No automatic group joining required

### 4. Message Filtering
Implement filtering capabilities by:
- **Event Type** - Filter messages by event_type
- **User ID** - Filter messages by user_id  
- **User Type** - Filter messages by user_type

## Technical Specifications

### AES Encryption/Decryption
```javascript
// Encryption function
export function encryptoData(data, key, iv) {
  const str = JSON.stringify(data);
  const encrypted = CryptoJS.AES.encrypt(str, CryptoJS.enc.Utf8.parse(key), {
    mode: CryptoJS.mode.CBC,
    iv: CryptoJS.enc.Utf8.parse(iv),
    padding: CryptoJS.pad.Pkcs7
  });
  return encrypted.toString();
}

// Decryption function
export function decryptoData(base64String, key, iv) {
  const decrypted = CryptoJS.AES.decrypt(base64String, CryptoJS.enc.Utf8.parse(key), {
    mode: CryptoJS.mode.CBC,
    iv: CryptoJS.enc.Utf8.parse(iv),
    padding: CryptoJS.pad.Pkcs7
  });
  return decrypted.toString(CryptoJS.enc.Utf8);
}
```

## Important Notes

### Group Limitations
- `im_group_all` has member limitations
- **Critical**: Always leave `im_group_all` when closing the application
- Proper cleanup prevents group capacity issues

### Message Format
- Messages are received in batches (similar to current WebSocket batch messages)
- Use provided `aes_key` and `aes_iv` for decryption
- Handle decompression as per existing WebSocket implementation

## Success Criteria
1. Successfully connect to Tencent IM using API credentials
2. Properly decrypt and display real-time messages
3. Implement role-based group management
4. Provide effective message filtering capabilities
5. Ensure proper cleanup and group leaving functionality
6. Maintain compatibility with existing message processing logic