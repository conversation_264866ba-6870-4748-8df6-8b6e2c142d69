# Tencent IM Debug Tool - Implementation Plan

## Phase 1: Project Setup & Dependencies
### 1.1 Initialize Project
- Create React/Next.js project with TypeScript
- Set up project structure and configuration
- Initialize git repository

### 1.2 Install Dependencies
```bash
npm install tencent-im-sdk crypto-js
npm install @types/crypto-js --save-dev
npm install antd # or preferred UI library
npm install axios # for API calls
```

## Phase 2: Core UI Development
### 2.1 Main Form Component
- Create input form with fields:
  - Domain (text input)
  - Token (text input)
  - Role selector (radio buttons: Anchor=1, User=0)
  - Group ID (optional text input)
- Add "Receive Messages" button
- Implement form validation

### 2.2 Message Display Area
- Create scrollable message list component
- Design message item layout (one item per line)
- Add timestamp and message content display

### 2.3 Filter Controls
- Event Type filter (dropdown/input)
- User ID filter (text input)
- User Type filter (dropdown/input)
- Clear filters button

## Phase 3: API Integration Service
### 3.1 API Service Layer
```javascript
// Service to handle API calls
class TencentIMService {
  async getIMInfo(domain, token, role, channelId, packageName, subChannelKey)
  // Handle host vs user endpoint selection
  // Process API response
}
```

### 3.2 Response Processing
- Extract credentials from API response:
  - username, token, app_id
  - aes_key, aes_iv
  - im_group_all
- Handle API errors and edge cases

## Phase 4: Tencent IM SDK Integration
### 4.1 SDK Initialization
```javascript
// Initialize Tencent IM SDK
const initTencentIM = (appId, username, userSig) => {
  // SDK setup logic
  // Authentication handling
  // Connection establishment
}
```

### 4.2 Group Management
- Implement join group functionality
- Handle user role logic:
  - User (role=0): Auto-join im_group_all + optional groupID
  - Anchor (role=1): Handle anchor-specific logic
- Implement leave group functionality with proper cleanup

## Phase 5: Message Processing & Encryption
### 5.1 AES Decryption Module
```javascript
// crypto.js
export function decryptoData(base64String, key, iv) {
  const decrypted = CryptoJS.AES.decrypt(base64String, CryptoJS.enc.Utf8.parse(key), {
    mode: CryptoJS.mode.CBC,
    iv: CryptoJS.enc.Utf8.parse(iv),
    padding: CryptoJS.pad.Pkcs7
  });
  return decrypted.toString(CryptoJS.enc.Utf8);
}
```

### 5.2 Message Handler
- Receive batch messages from Tencent IM
- Decrypt using AES key/IV from API response
- Decompress messages (similar to WebSocket implementation)
- Parse and format for display

## Phase 6: Real-time Message Display
### 6.1 Message State Management
- Use React state/context for message storage
- Implement real-time message updates
- Handle message filtering logic

### 6.2 Filtering Implementation
```javascript
const filterMessages = (messages, filters) => {
  return messages.filter(msg => {
    if (filters.eventType && msg.event_type !== filters.eventType) return false;
    if (filters.userId && msg.user_id !== filters.userId) return false;
    if (filters.userType && msg.user_type !== filters.userType) return false;
    return true;
  });
}
```

## Phase 7: Cleanup & Error Handling
### 7.1 Proper Cleanup
- Implement beforeunload event handler
- Ensure proper group leaving on app close
- Handle SDK disconnection

### 7.2 Error Handling
- API call error handling
- SDK connection error handling
- Message decryption error handling
- User feedback for errors

### 7.3 Loading States
- Show loading indicators during API calls
- Display connection status
- Handle reconnection logic

## Phase 8: Testing & Optimization
### 8.1 Testing
- Test with both anchor and user roles
- Test group joining/leaving functionality
- Test message filtering
- Test cleanup on app close

### 8.2 Performance Optimization
- Optimize message rendering for large volumes
- Implement virtual scrolling if needed
- Memory management for message storage

## Phase 9: Documentation & Deployment
### 9.1 Documentation
- Update README with setup instructions
- Document API usage and configuration
- Add troubleshooting guide

### 9.2 Build & Deployment
- Configure build process
- Set up environment variables
- Deploy to hosting platform

## Development Timeline
- **Phase 1-2**: 1-2 days (Setup + UI)
- **Phase 3-4**: 2-3 days (API + SDK Integration)
- **Phase 5-6**: 2-3 days (Message Processing + Display)
- **Phase 7-8**: 1-2 days (Cleanup + Testing)
- **Phase 9**: 1 day (Documentation + Deployment)

**Total Estimated Time**: 7-11 days

## Key Implementation Notes
1. **Critical**: Always implement proper group leaving to prevent capacity issues
2. Handle batch messages similar to existing WebSocket implementation
3. Use provided AES key/IV for message decryption
4. Maintain compatibility with existing message processing logic
5. Ensure graceful error handling and user feedback